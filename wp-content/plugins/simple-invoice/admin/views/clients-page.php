<?php
/**
 * Clients Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get clients and search parameters
$client_manager = new SI_Client();
$search = isset($_GET['search']) ? sanitize_text_field($_GET['search']) : '';
$paged = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
$per_page = 20;
$offset = ($paged - 1) * $per_page;

// Get clients with search
$clients = $client_manager->si_get_clients(array(
    'search' => $search,
    'limit' => $per_page,
    'offset' => $offset
));

// Get total count for pagination
$total_clients = count($client_manager->si_get_clients(array('search' => $search)));
$total_pages = ceil($total_clients / $per_page);

// Get statistics
$all_clients = $client_manager->si_get_clients();
$total_count = count($all_clients);

// Get invoice manager for client statistics
$invoice_manager = new SI_Invoice();
$client_stats = array();
$active_clients = 0;
$total_revenue = 0;

foreach ($all_clients as $client) {
    $client_invoices = $invoice_manager->si_get_invoices(array('client_id' => $client->id));
    $client_revenue = array_sum(array_map(function($inv) { return floatval($inv->total_amount); }, $client_invoices));

    $client_stats[$client->id] = array(
        'total_invoices' => count($client_invoices),
        'total_amount' => $client_revenue,
        'last_invoice' => !empty($client_invoices) ? max(array_map(function($inv) { return strtotime($inv->created_at); }, $client_invoices)) : null
    );

    if (count($client_invoices) > 0) {
        $active_clients++;
    }
    $total_revenue += $client_revenue;
}
?>

<div class="wrap si-clients-page">
    <!-- Page Header -->
    <div class="si-page-header">
        <div class="si-header-content">
            <div class="si-header-title">
                <h1 class="si-page-title">
                    <span class="dashicons dashicons-groups"></span>
                    <?php echo esc_html__('Clients', 'simple-invoice'); ?>
                </h1>
                <p class="si-page-subtitle"><?php echo esc_html__('Manage your client relationships and contact information', 'simple-invoice'); ?></p>
            </div>
            <div class="si-header-actions">
                <a href="#" class="si-btn si-btn-primary si-add-client-btn">
                    <span class="dashicons dashicons-plus-alt"></span>
                    <?php echo esc_html__('Add New Client', 'simple-invoice'); ?>
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="si-quick-stats">
        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-blue">
                <span class="dashicons dashicons-groups"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($total_count); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Clients', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-green">
                <span class="dashicons dashicons-businessman"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html($active_clients); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Active Clients', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-orange">
                <span class="dashicons dashicons-chart-line"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html(si_format_currency($total_revenue)); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Revenue', 'simple-invoice'); ?></span>
            </div>
        </div>

        <div class="si-stat-item">
            <div class="si-stat-icon si-stat-purple">
                <span class="dashicons dashicons-media-text"></span>
            </div>
            <div class="si-stat-info">
                <span class="si-stat-number"><?php echo esc_html(array_sum(array_column($client_stats, 'total_invoices'))); ?></span>
                <span class="si-stat-label"><?php echo esc_html__('Total Invoices', 'simple-invoice'); ?></span>
            </div>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="si-clients-content">
        <div class="si-search-section">
            <form method="get" action="" class="si-search-form">
                <input type="hidden" name="page" value="si-clients" />

                <div class="si-search-container">
                    <div class="si-search-input-wrapper">
                        <span class="dashicons dashicons-search si-search-icon"></span>
                        <input type="search"
                               name="search"
                               id="si-client-search"
                               value="<?php echo esc_attr($search); ?>"
                               placeholder="<?php echo esc_attr__('Search clients by name, business, or email...', 'simple-invoice'); ?>"
                               class="si-search-input" />
                        <?php if ($search): ?>
                            <a href="<?php echo esc_url(admin_url('admin.php?page=si-clients')); ?>" class="si-search-clear">
                                <span class="dashicons dashicons-dismiss"></span>
                            </a>
                        <?php endif; ?>
                    </div>

                    <button type="submit" class="si-btn si-btn-primary si-search-btn">
                        <span class="dashicons dashicons-search"></span>
                        <?php echo esc_html__('Search', 'simple-invoice'); ?>
                    </button>
                </div>

                <?php if ($search): ?>
                    <div class="si-search-results-info">
                        <span class="si-results-text">
                            <?php echo esc_html(sprintf(__('Found %d clients matching "%s"', 'simple-invoice'), $total_clients, $search)); ?>
                        </span>
                    </div>
                <?php endif; ?>
            </form>
        </div>

        <!-- Clients Grid -->
        <!-- Clients List -->
        <div class="si-clients-list-wrapper">
            <?php if (!empty($clients)): ?>
                <div class="si-clients-list">
                    <div class="si-list-header">
                        <h3><?php echo esc_html__('Client List', 'simple-invoice'); ?></h3>
                        <div class="si-list-info">
                            <?php if ($search): ?>
                                <?php echo esc_html(sprintf(__('Showing %d of %d clients matching "%s"', 'simple-invoice'), count($clients), $total_clients, $search)); ?>
                            <?php else: ?>
                                <?php echo esc_html(sprintf(__('Showing %d clients', 'simple-invoice'), count($clients))); ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="si-clients-list-container">
                        <?php foreach ($clients as $client): ?>
                            <?php
                            $stats = isset($client_stats[$client->id]) ? $client_stats[$client->id] : array('total_invoices' => 0, 'total_amount' => 0, 'last_invoice' => null);
                            ?>
                            <div class="si-client-item" data-client-id="<?php echo esc_attr($client->id); ?>">
                                <div class="si-client-row">
                                    <!-- Client Info -->
                                    <div class="si-client-main">
                                        <div class="si-client-avatar">
                                            <span class="dashicons dashicons-businessman"></span>
                                        </div>
                                        <div class="si-client-details">
                                            <h4 class="si-client-name"><?php echo esc_html($client->name); ?></h4>
                                            <div class="si-client-meta">
                                                <?php if (!empty($client->business_name)): ?>
                                                    <span class="si-business-name"><?php echo esc_html($client->business_name); ?></span>
                                                <?php else: ?>
                                                    <span class="si-individual"><?php echo esc_html__('Individual Client', 'simple-invoice'); ?></span>
                                                <?php endif; ?>
                                                <span class="si-separator">•</span>
                                                <span class="si-date"><?php echo esc_html(date('M j, Y', strtotime($client->created_at))); ?></span>
                                                <span class="si-separator">•</span>
                                                <?php if ($stats['total_invoices'] > 0): ?>
                                                    <span class="si-status si-active"><?php echo esc_html__('Active', 'simple-invoice'); ?></span>
                                                <?php else: ?>
                                                    <span class="si-status si-new"><?php echo esc_html__('New', 'simple-invoice'); ?></span>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Contact Info -->
                                    <div class="si-client-contact">
                                        <?php if (!empty($client->email)): ?>
                                            <div class="si-contact-item">
                                                <span class="dashicons dashicons-email"></span>
                                                <a href="mailto:<?php echo esc_attr($client->email); ?>"><?php echo esc_html($client->email); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client->contact_number)): ?>
                                            <div class="si-contact-item">
                                                <span class="dashicons dashicons-phone"></span>
                                                <a href="tel:<?php echo esc_attr($client->contact_number); ?>"><?php echo esc_html($client->contact_number); ?></a>
                                            </div>
                                        <?php endif; ?>
                                        <?php if (!empty($client->gstin)): ?>
                                            <div class="si-contact-item">
                                                <span class="dashicons dashicons-id"></span>
                                                <span><?php echo esc_html($client->gstin); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Stats -->
                                    <div class="si-client-stats">
                                        <div class="si-stat">
                                            <span class="si-stat-number"><?php echo esc_html($stats['total_invoices']); ?></span>
                                            <span class="si-stat-label"><?php echo esc_html__('Invoices', 'simple-invoice'); ?></span>
                                        </div>
                                        <div class="si-stat">
                                            <span class="si-stat-number"><?php echo esc_html(si_format_currency($stats['total_amount'])); ?></span>
                                            <span class="si-stat-label"><?php echo esc_html__('Revenue', 'simple-invoice'); ?></span>
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    <div class="si-client-actions">
                                        <button type="button" class="button button-small si-edit-client" data-client-id="<?php echo esc_attr($client->id); ?>" title="<?php echo esc_attr__('Edit Client', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-edit"></span>
                                            <?php echo esc_html__('Edit', 'simple-invoice'); ?>
                                        </button>
                                        <a href="<?php echo esc_url(admin_url('admin.php?page=si-create-invoice&client_id=' . $client->id)); ?>" class="button button-small button-primary" title="<?php echo esc_attr__('Create Invoice', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-plus-alt"></span>
                                            <?php echo esc_html__('Invoice', 'simple-invoice'); ?>
                                        </a>
                                        <button type="button" class="button button-small button-link-delete si-delete-client" data-client-id="<?php echo esc_attr($client->id); ?>" title="<?php echo esc_attr__('Delete Client', 'simple-invoice'); ?>">
                                            <span class="dashicons dashicons-trash"></span>
                                            <?php echo esc_html__('Delete', 'simple-invoice'); ?>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="si-empty-state">
                    <div class="si-empty-content">
                        <?php if ($search): ?>
                            <div class="si-empty-icon">
                                <span class="dashicons dashicons-search"></span>
                            </div>
                            <h3 class="si-empty-title"><?php echo esc_html__('No clients found', 'simple-invoice'); ?></h3>
                            <p class="si-empty-description"><?php echo sprintf(esc_html__('No clients match your search for "%s". Try adjusting your search terms or add a new client.', 'simple-invoice'), '<strong>' . esc_html($search) . '</strong>'); ?></p>
                            <div class="si-empty-actions">
                                <a href="<?php echo esc_url(admin_url('admin.php?page=si-clients')); ?>" class="si-btn si-btn-secondary">
                                    <span class="dashicons dashicons-dismiss"></span>
                                    <?php echo esc_html__('Clear Search', 'simple-invoice'); ?>
                                </a>
                                <a href="#" class="si-btn si-btn-primary si-add-client-btn">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <?php echo esc_html__('Add New Client', 'simple-invoice'); ?>
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="si-empty-icon">
                                <span class="dashicons dashicons-groups"></span>
                            </div>
                            <h3 class="si-empty-title"><?php echo esc_html__('No clients yet', 'simple-invoice'); ?></h3>
                            <p class="si-empty-description"><?php echo esc_html__('Start building your client base by adding your first client. You can then create invoices and track your business relationships.', 'simple-invoice'); ?></p>
                            <div class="si-empty-actions">
                                <a href="#" class="si-btn si-btn-primary si-add-client-btn">
                                    <span class="dashicons dashicons-plus-alt"></span>
                                    <?php echo esc_html__('Add Your First Client', 'simple-invoice'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($total_pages > 1): ?>
        <div class="si-pagination">
            <?php
            $pagination_args = array(
                'base' => add_query_arg('paged', '%#%'),
                'format' => '',
                'prev_text' => __('&laquo; Previous', 'simple-invoice'),
                'next_text' => __('Next &raquo;', 'simple-invoice'),
                'current' => $paged,
                'total' => $total_pages,
                'add_args' => array(
                    'search' => $search
                )
            );
            echo paginate_links($pagination_args);
            ?>
        </div>
    <?php endif; ?>
</div>

<!-- Modern Add/Edit Client Modal -->
<div id="si-client-modal" class="si-modal si-client-modal" style="display: none;">
    <div class="si-modal-content">
        <!-- Modal Header -->
        <div class="si-modal-header">
            <div class="si-modal-header-content">
                <div class="si-modal-icon">
                    <span class="dashicons dashicons-businessman"></span>
                </div>
                <div class="si-modal-title-section">
                    <h2 id="si-client-modal-title"><?php echo esc_html__('Add New Client', 'simple-invoice'); ?></h2>
                    <p class="si-modal-subtitle"><?php echo esc_html__('Enter client information to create a new client profile', 'simple-invoice'); ?></p>
                </div>
            </div>
            <button type="button" class="si-modal-close">
                <span class="dashicons dashicons-no-alt"></span>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="si-modal-body">
            <form id="si-client-form">
                <input type="hidden" id="si-client-id" name="client_id" value="" />

                <!-- Personal Information Section -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3>
                            <span class="dashicons dashicons-admin-users"></span>
                            <?php echo esc_html__('Personal Information', 'simple-invoice'); ?>
                        </h3>
                        <p class="si-section-description"><?php echo esc_html__('Basic client details and contact information', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-form-grid">
                        <div class="si-form-group si-form-group-full">
                            <label for="si-client-name" class="si-form-label">
                                <span class="dashicons dashicons-admin-users"></span>
                                <?php echo esc_html__('Full Name', 'simple-invoice'); ?>
                                <span class="si-required">*</span>
                            </label>
                            <input type="text"
                                   id="si-client-name"
                                   name="name"
                                   class="si-form-input"
                                   required
                                   placeholder="<?php echo esc_attr__('Enter client full name', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('The primary contact person name', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group">
                            <label for="si-client-email" class="si-form-label">
                                <span class="dashicons dashicons-email-alt"></span>
                                <?php echo esc_html__('Email Address', 'simple-invoice'); ?>
                            </label>
                            <input type="email"
                                   id="si-client-email"
                                   name="email"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('Primary email for invoices and communication', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group">
                            <label for="si-client-phone" class="si-form-label">
                                <span class="dashicons dashicons-phone"></span>
                                <?php echo esc_html__('Phone Number', 'simple-invoice'); ?>
                            </label>
                            <input type="tel"
                                   id="si-client-phone"
                                   name="contact_number"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Phone number with country code', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('Contact number for quick communication', 'simple-invoice'); ?></div>
                        </div>
                    </div>
                </div>

                <!-- Business Information Section -->
                <div class="si-form-section">
                    <div class="si-section-header">
                        <h3>
                            <span class="dashicons dashicons-building"></span>
                            <?php echo esc_html__('Business Information', 'simple-invoice'); ?>
                        </h3>
                        <p class="si-section-description"><?php echo esc_html__('Company details and tax information (optional)', 'simple-invoice'); ?></p>
                    </div>

                    <div class="si-form-grid">
                        <div class="si-form-group">
                            <label for="si-client-business" class="si-form-label">
                                <span class="dashicons dashicons-building"></span>
                                <?php echo esc_html__('Business Name', 'simple-invoice'); ?>
                            </label>
                            <input type="text"
                                   id="si-client-business"
                                   name="business_name"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Company or business name', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('Official business or company name', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group">
                            <label for="si-client-gstin" class="si-form-label">
                                <span class="dashicons dashicons-id-alt"></span>
                                <?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?>
                            </label>
                            <input type="text"
                                   id="si-client-gstin"
                                   name="gstin"
                                   class="si-form-input"
                                   placeholder="<?php echo esc_attr__('Tax identification number', 'simple-invoice'); ?>" />
                            <div class="si-form-help"><?php echo esc_html__('GST number or tax identification', 'simple-invoice'); ?></div>
                        </div>

                        <div class="si-form-group si-form-group-full">
                            <label for="si-client-address" class="si-form-label">
                                <span class="dashicons dashicons-location-alt"></span>
                                <?php echo esc_html__('Business Address', 'simple-invoice'); ?>
                            </label>
                            <textarea id="si-client-address"
                                      name="address"
                                      rows="3"
                                      class="si-form-textarea"
                                      placeholder="<?php echo esc_attr__('Complete business address with city, state, and postal code', 'simple-invoice'); ?>"></textarea>
                            <div class="si-form-help"><?php echo esc_html__('Full address for invoicing and correspondence', 'simple-invoice'); ?></div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="si-modal-footer">
            <div class="si-modal-footer-content">
                <button type="button" class="si-btn si-btn-primary" id="si-save-client">
                    <span class="dashicons dashicons-yes-alt"></span>
                    <span class="si-btn-text"><?php echo esc_html__('Save Client', 'simple-invoice'); ?></span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Modern Clients Page Styles */
.si-clients-page {
    background: #ffffff;
    margin: 0 -20px;
    padding: 0;
    min-height: 100vh;
}

/* Page Header */
.si-page-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: white;
    border-bottom: 1px solid #e7e7e7;
    padding: 30px 20px;
    margin-bottom: 30px;
}

.si-header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.si-header-title {
    flex: 1;
}

.si-page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 700;
    color: #ffffff;
    display: flex;
    align-items: center;
    gap: 8px;
}

.si-page-title .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: #0073aa;
}

.si-page-subtitle {
    margin: 0;
    font-size: 16px;
    color: #ffffff;
    opacity: 0.9;
    font-weight: 400;
}

.si-header-actions {
    flex-shrink: 0;
}

/* Quick Stats */
.si-quick-stats {
    max-width: 1200px;
    margin: 0 auto 20px auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
}

.si-stat-item {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.si-stat-item:hover {
    transform: none;
    box-shadow: none;
}

.si-stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
}

.si-stat-icon .dashicons {
    font-size: 22px;
    width: 22px;
    height: 22px;
    color: #ffffff;
}

.si-stat-info {
    flex: 1;
}

.si-stat-number {
    display: block;
    font-size: 20px;
    font-weight: 700;
    color: #000000;
    line-height: 1;
    margin-bottom: 3px;
}

.si-stat-label {
    display: block;
    font-size: 14px;
    color: #5f5f5f;
    font-weight: 500;
}

/* Clients Content */
.si-clients-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Search Section */
.si-search-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.si-search-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.si-search-input-wrapper {
    flex: 1;
    position: relative;
    display: flex;
    align-items: center;
}

.si-search-icon {
    position: absolute;
    left: 10px;
    color: #666;
    font-size: 14px;
    width: 14px;
    height: 14px;
    z-index: 2;
}

.si-search-input {
    width: 100%;
    padding: 8px 12px 8px 30px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
    background: white;
}

.si-search-input:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.si-search-clear {
    position: absolute;
    right: 8px;
    color: #999;
    text-decoration: none;
    padding: 2px;
    border-radius: 2px;
}

.si-search-clear:hover {
    color: #dc3232;
    text-decoration: none;
}

.si-search-results-info {
    margin-top: 10px;
    padding: 6px 10px;
    background: #e7f3ff;
    border-radius: 3px;
    border-left: 3px solid #0073aa;
}

.si-results-text {
    font-size: 12px;
    color: #0073aa;
    font-weight: 500;
}

/* Simple Buttons */
.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 6px 12px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 13px;
    font-weight: 400;
    text-decoration: none;
    cursor: pointer;
    background: #f7f7f7;
    color: #333;
    white-space: nowrap;
    transition: none;
}

.si-btn:hover {
    background: #fafafa;
    border-color: #999;
    color: #333;
    text-decoration: none;
}

.si-btn:active {
    background: #eee;
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border-color: #f47a45;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
    border-color: #5f5f5f;
}

.si-btn .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Clients List */
.si-clients-list {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.si-list-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ddd;
    background: #f9f9f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.si-list-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.si-list-info {
    font-size: 13px;
    color: #666;
}

/* Clients List Container */
.si-clients-list-container {
    background: white;
}

/* Client Item */
.si-client-item {
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s ease;
}

.si-client-item:last-child {
    border-bottom: none;
}

.si-client-item:hover {
    background: #f9f9f9;
}

.si-client-row {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    gap: 20px;
}

/* Client Main Info */
.si-client-main {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    min-width: 0;
}

.si-client-avatar {
    width: 40px;
    height: 40px;
    background: #0073aa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.si-client-avatar .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: white;
}

.si-client-details {
    flex: 1;
    min-width: 0;
}

.si-client-name {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1.3;
}

.si-client-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    font-size: 13px;
    color: #666;
}

.si-business-name {
    font-weight: 500;
    color: #333;
}

.si-individual {
    font-style: italic;
    color: #999;
}

.si-separator {
    color: #ccc;
}

.si-date {
    color: #666;
}

.si-status {
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.si-status.si-active {
    background: #d1e7dd;
    color: #0f5132;
}

.si-status.si-new {
    background: #fff3cd;
    color: #664d03;
}

/* Client Contact */
.si-client-contact {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 200px;
}

.si-contact-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
}

.si-contact-item .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
    color: #0073aa;
    flex-shrink: 0;
}

.si-contact-item a {
    color: #0073aa;
    text-decoration: none;
}

.si-contact-item a:hover {
    text-decoration: underline;
}

.si-contact-item span {
    color: #666;
}

/* Client Stats */
.si-client-stats {
    display: flex;
    gap: 15px;
    min-width: 150px;
}

.si-stat {
    text-align: center;
    padding: 8px 12px;
    background: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #eee;
    min-width: 60px;
}

.si-stat-number {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    line-height: 1;
    margin-bottom: 2px;
}

.si-stat-label {
    display: block;
    font-size: 11px;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Client Actions */
.si-client-actions {
    display: flex;
    gap: 6px;
    flex-shrink: 0;
}

.si-client-actions .button {
    padding: 4px 8px;
    font-size: 12px;
    line-height: 1.4;
    min-height: auto;
    display: inline-flex;
    align-items: center;
    gap: 4px;
    white-space: nowrap;
}

.si-client-actions .button .dashicons {
    font-size: 14px;
    width: 14px;
    height: 14px;
}

/* Contact Info */
.si-contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.si-contact-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 12px;
}

.si-contact-item .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
    color: #0073aa;
    flex-shrink: 0;
}

.si-contact-item a {
    color: #0073aa;
    text-decoration: none;
}

.si-contact-item a:hover {
    text-decoration: underline;
}

.si-empty-field {
    font-style: italic;
    color: #999;
    font-size: 11px;
}

/* Business Info */
.si-business-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.si-business-name,
.si-gstin {
    font-size: 12px;
    color: #333;
    line-height: 1.4;
}

.si-business-name {
    font-weight: 500;
}

/* Stats Info */
.si-stats-info {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.si-stats-info .si-stat-item {
    text-align: center;
    padding: 5px;
    background: #f9f9f9;
    border-radius: 3px;
    border: 1px solid #eee;
}

.si-stats-info .si-stat-number {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    line-height: 1;
    margin-bottom: 2px;
}

.si-stats-info .si-stat-label {
    font-size: 10px;
    color: #666;
    text-transform: uppercase;
}

/* Actions */
.si-actions-wrapper {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
}

.si-btn-sm {
    padding: 5px 8px;
    font-size: 11px;
    min-width: auto;
    white-space: nowrap;
    border-radius: 3px;
    box-shadow: none;
}

.si-btn-sm:hover {
    transform: none;
    box-shadow: none;
}

.si-btn-sm .si-btn-text {
    display: inline;
    margin-left: 3px;
}

.si-btn-sm .dashicons {
    font-size: 12px;
    width: 12px;
    height: 12px;
}

.si-btn-secondary {
    background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
    color: #000000;
    border: 1px solid #e7e7e7;
}

.si-btn-secondary:hover {
    background: linear-gradient(135deg, #ffffff 0%, #e7e7e7 100%);
    color: #000000;
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border: 1px solid #f47a45;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
}

.si-btn-danger {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border: 1px solid #f47a45;
}

.si-btn-danger:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
}

/* Empty State */
.si-empty-state {
    background: white;
    border-radius: 12px;
    padding: 60px 40px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.si-empty-content {
    max-width: 400px;
    margin: 0 auto;
}

.si-empty-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px auto;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.si-empty-icon .dashicons {
    font-size: 40px;
    width: 40px;
    height: 40px;
    color: white;
}

.si-empty-title {
    margin: 0 0 12px 0;
    font-size: 24px;
    font-weight: 600;
    color: #2d3748;
}

.si-empty-description {
    margin: 0 0 24px 0;
    font-size: 16px;
    color: #718096;
    line-height: 1.5;
}

.si-empty-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-header-content {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .si-page-title {
        font-size: 20px;
        justify-content: center;
    }

    .si-quick-stats {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 10px;
    }

    .si-stat-item {
        padding: 12px;
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .si-stat-icon {
        width: 32px;
        height: 32px;
    }

    .si-stat-icon .dashicons {
        font-size: 16px;
        width: 16px;
        height: 16px;
    }

    .si-stat-number {
        font-size: 16px;
    }

    .si-search-container {
        flex-direction: column;
        align-items: stretch;
    }

    .si-empty-state {
        padding: 30px 15px;
    }

    .si-empty-title {
        font-size: 18px;
    }

    .si-empty-description {
        font-size: 14px;
    }

    .si-empty-actions {
        flex-direction: column;
    }

    /* Mobile List Layout */
    .si-client-row {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
        padding: 15px;
    }

    .si-client-main {
        gap: 10px;
    }

    .si-client-contact {
        min-width: auto;
        padding: 10px;
        background: #f9f9f9;
        border-radius: 4px;
    }

    .si-client-stats {
        justify-content: center;
        gap: 10px;
    }

    .si-stat {
        min-width: 50px;
        padding: 6px 8px;
    }

    .si-stat-number {
        font-size: 14px;
    }

    .si-stat-label {
        font-size: 10px;
    }

    .si-client-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .si-client-actions .button {
        flex: 1;
        min-width: 80px;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .si-page-header {
        padding: 15px;
    }

    .si-clients-content {
        padding: 0 15px;
    }

    .si-quick-stats {
        padding: 0 15px;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .si-list-header {
        padding: 12px 15px;
        flex-direction: column;
        gap: 8px;
        text-align: center;
    }

    .si-client-row {
        padding: 12px;
    }

    .si-client-main {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 8px;
    }

    .si-client-avatar {
        width: 35px;
        height: 35px;
    }

    .si-client-avatar .dashicons {
        font-size: 18px;
        width: 18px;
        height: 18px;
    }

    .si-client-meta {
        justify-content: center;
        flex-wrap: wrap;
    }

    .si-client-contact {
        padding: 8px;
    }

    .si-client-stats {
        gap: 8px;
    }

    .si-stat {
        min-width: 45px;
        padding: 5px 6px;
    }

    .si-client-actions {
        gap: 4px;
    }

    .si-client-actions .button {
        font-size: 11px;
        padding: 3px 6px;
        min-width: 70px;
    }

    .si-client-actions .button .dashicons {
        font-size: 12px;
        width: 12px;
        height: 12px;
    }
}

/* Modern Client Modal Styles */
.si-client-modal .si-modal-content {
    max-width: 700px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

/* Modal Header */
.si-client-modal .si-modal-header {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    padding: 30px;
    border-radius: 16px 16px 0 0;
    position: relative;
    text-align: center;
}

.si-modal-header-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.si-modal-icon {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.si-modal-icon .dashicons {
    font-size: 24px;
    width: 24px;
    height: 24px;
    color: white;
}

.si-modal-title-section h2 {
    margin: 0 0 5px 0;
    font-size: 22px;
    font-weight: 600;
    color: white;
}

.si-modal-subtitle {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
    color: white;
}

.si-client-modal .si-modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255,255,255,0.2);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.si-client-modal .si-modal-close:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.si-client-modal .si-modal-close .dashicons {
    font-size: 18px;
    width: 18px;
    height: 18px;
    color: white;
}

/* Modal Body */
.si-client-modal .si-modal-body {
    padding: 30px;
    background: #f8f9fa;
}

/* Form Sections */
.si-form-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    border: 1px solid #e1e5e9;
}

.si-form-section:last-child {
    margin-bottom: 0;
}

.si-section-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f1f3f4;
}

.si-section-header h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-section-header h3 .dashicons {
    font-size: 20px;
    width: 20px;
    height: 20px;
    color: #667eea;
}

.si-section-description {
    margin: 0;
    font-size: 14px;
    color: #718096;
    line-height: 1.4;
}

/* Form Grid */
.si-form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.si-form-group-full {
    grid-column: 1 / -1;
}

/* Form Groups */
.si-form-group {
    display: flex;
    flex-direction: column;
}

.si-form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    font-size: 14px;
}

.si-form-label .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
    color: #667eea;
}

.si-required {
    color: #e53e3e;
    font-weight: 700;
}

/* Form Inputs */
.si-form-input,
.si-form-textarea {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background: white;
    width: 100%;
    box-sizing: border-box;
}

.si-form-input:focus,
.si-form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.si-form-textarea {
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.si-form-help {
    margin-top: 6px;
    font-size: 12px;
    color: #718096;
    line-height: 1.3;
}

/* Modal Footer */
.si-client-modal .si-modal-footer {
    background: white;
    padding: 20px 30px;
    border-radius: 0 0 12px 12px;
    border-top: 1px solid #e2e8f0;
}

.si-modal-footer-content {
    display: flex;
    justify-content: center;
    gap: 12px;
}

/* Modern Buttons */
.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    justify-content: center;
}

.si-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    text-decoration: none;
}

.si-btn:active {
    transform: translateY(0);
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    box-shadow: 0 4px 12px rgba(244, 122, 69, 0.3);
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff;
}

.si-btn-secondary {
    background: linear-gradient(135deg, #e7e7e7 0%, #ffffff 100%);
    color: #000000;
    border: 2px solid #e7e7e7;
}

.si-btn-secondary:hover {
    background: linear-gradient(135deg, #ffffff 0%, #e7e7e7 100%);
    color: #000000;
    border-color: #e7e7e7;
}

.si-btn .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Loading State */
.si-btn.si-loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.si-btn.si-loading .si-btn-text {
    opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-client-modal .si-modal-content {
        width: 95%;
        margin: 20px auto;
        max-height: calc(100vh - 40px);
    }

    .si-client-modal .si-modal-header {
        padding: 20px;
    }

    .si-modal-header-content {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .si-modal-icon {
        width: 40px;
        height: 40px;
    }

    .si-modal-icon .dashicons {
        font-size: 20px;
        width: 20px;
        height: 20px;
    }

    .si-modal-title-section h2 {
        font-size: 20px;
    }

    .si-client-modal .si-modal-body {
        padding: 20px;
    }

    .si-form-section {
        padding: 20px;
    }

    .si-form-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .si-modal-footer-content {
        flex-direction: column;
    }

    .si-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .si-client-modal .si-modal-content {
        width: 100%;
        height: 100vh;
        border-radius: 0;
        margin: 0;
    }

    .si-client-modal .si-modal-header {
        border-radius: 0;
    }

    .si-client-modal .si-modal-footer {
        border-radius: 0;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    var clientModal = $('#si-client-modal');
    var clientForm = $('#si-client-form');
    var isEditing = false;

    // Open add client modal
    $('.si-add-client-btn').on('click', function(e) {
        e.preventDefault();
        openClientModal();
    });

    // Open edit client modal
    $(document).on('click', '.si-edit-client', function() {
        var clientId = $(this).data('client-id');
        openClientModal(clientId);
    });

    // Delete client
    $(document).on('click', '.si-delete-client', function() {
        var clientId = $(this).data('client-id');
        var clientName = $(this).closest('tr').find('.si-client-name').text();

        if (confirm('<?php echo esc_js(__('Are you sure you want to delete', 'simple-invoice')); ?> "' + clientName + '"?')) {
            deleteClient(clientId);
        }
    });

    // Close modal
    $('.si-modal-close').on('click', function() {
        closeClientModal();
    });

    // Save client
    $('#si-save-client').on('click', function() {
        saveClient();
    });

    function openClientModal(clientId) {
        isEditing = !!clientId;

        if (isEditing) {
            $('#si-client-modal-title').text('<?php echo esc_js(__('Edit Client', 'simple-invoice')); ?>');
            $('.si-modal-subtitle').text('<?php echo esc_js(__('Update client information and save changes', 'simple-invoice')); ?>');
            $('#si-save-client .si-btn-text').text('<?php echo esc_js(__('Update Client', 'simple-invoice')); ?>');
            $('#si-save-client .dashicons').removeClass('dashicons-yes-alt').addClass('dashicons-update');
            loadClientData(clientId);
        } else {
            $('#si-client-modal-title').text('<?php echo esc_js(__('Add New Client', 'simple-invoice')); ?>');
            $('.si-modal-subtitle').text('<?php echo esc_js(__('Enter client information to create a new client profile', 'simple-invoice')); ?>');
            $('#si-save-client .si-btn-text').text('<?php echo esc_js(__('Save Client', 'simple-invoice')); ?>');
            $('#si-save-client .dashicons').removeClass('dashicons-update').addClass('dashicons-yes-alt');
            clientForm[0].reset();
            $('#si-client-id').val('');
        }

        clientModal.show();
    }

    function closeClientModal() {
        clientModal.hide();
        clientForm[0].reset();
        isEditing = false;
    }

    function loadClientData(clientId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_get_client',
                client_id: clientId,
                nonce: '<?php echo wp_create_nonce('si_get_client_nonce'); ?>'
            },
            success: function(response) {
                if (response.success && response.data.client) {
                    var client = response.data.client;
                    $('#si-client-id').val(client.id);
                    $('#si-client-name').val(client.name);
                    $('#si-client-business').val(client.business_name);
                    $('#si-client-address').val(client.address);
                    $('#si-client-phone').val(client.contact_number);
                    $('#si-client-email').val(client.email);
                    $('#si-client-gstin').val(client.gstin);
                }
            }
        });
    }

    function saveClient() {
        // Validate required fields
        var name = $('#si-client-name').val().trim();
        if (!name) {
            alert('<?php echo esc_js(__('Please enter client name.', 'simple-invoice')); ?>');
            $('#si-client-name').focus();
            return;
        }

        var saveButton = $('#si-save-client');
        var originalText = saveButton.find('.si-btn-text').text();

        // Show loading state
        saveButton.addClass('si-loading').prop('disabled', true);
        saveButton.find('.si-btn-text').text('<?php echo esc_js(__('Saving...', 'simple-invoice')); ?>');
        saveButton.find('.dashicons').removeClass('dashicons-yes-alt dashicons-update').addClass('dashicons-update');

        var formData = clientForm.serialize();
        var action = isEditing ? 'si_edit_client' : 'si_add_client';
        var nonce = isEditing ? '<?php echo wp_create_nonce('si_edit_client_nonce'); ?>' : '<?php echo wp_create_nonce('si_add_client_nonce'); ?>';

        formData += '&action=' + action + '&nonce=' + nonce;

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    // Show success state
                    saveButton.find('.si-btn-text').text('<?php echo esc_js(__('Saved!', 'simple-invoice')); ?>');
                    saveButton.find('.dashicons').removeClass('dashicons-update').addClass('dashicons-yes');

                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    alert(response.data || '<?php echo esc_js(__('Failed to save client. Please try again.', 'simple-invoice')); ?>');
                    resetSaveButton();
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error saving client:', error);
                alert('<?php echo esc_js(__('Error saving client. Please try again.', 'simple-invoice')); ?>');
                resetSaveButton();
            }
        });

        function resetSaveButton() {
            saveButton.removeClass('si-loading').prop('disabled', false);
            saveButton.find('.si-btn-text').text(originalText);
            if (isEditing) {
                saveButton.find('.dashicons').removeClass('dashicons-update dashicons-yes').addClass('dashicons-update');
            } else {
                saveButton.find('.dashicons').removeClass('dashicons-update dashicons-yes').addClass('dashicons-yes-alt');
            }
        }
    }

    function deleteClient(clientId) {
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'si_delete_client',
                client_id: clientId,
                nonce: '<?php echo wp_create_nonce('si_delete_client_nonce'); ?>'
            },
            success: function(response) {
                if (response.success) {
                    alert('<?php echo esc_js(__('Client deleted successfully.', 'simple-invoice')); ?>');
                    location.reload();
                } else {
                    alert(response.message || '<?php echo esc_js(__('Failed to delete client.', 'simple-invoice')); ?>');
                }
            },
            error: function() {
                alert('<?php echo esc_js(__('An error occurred. Please try again.', 'simple-invoice')); ?>');
            }
        });
    }

});
</script>
