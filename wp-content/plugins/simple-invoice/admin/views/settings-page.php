<?php
/**
 * Settings Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = si_get_settings();

// Test if functions are working
error_log('Settings page loaded. Current settings: ' . print_r($settings, true));

// Add debug info to page
if (isset($_GET['debug'])) {
    echo '<div class="notice notice-info"><p><strong>DEBUG: Current $settings variable:</strong><br><pre>' . print_r($settings, true) . '</pre></p></div>';
}

// Remove the old failing test - we have better debug tools now

// Debug form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log('POST request received');
    error_log('POST data: ' . print_r($_POST, true));
}

// Handle form submission
if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'si_settings_nonce')) {
    error_log('Form submission detected - save_settings button pressed');
    error_log('Nonce verification passed');

    // Add debug output for regular save
    if (isset($_GET['debug'])) {
        echo '<div class="notice notice-info"><p><strong>REGULAR SAVE TRIGGERED</strong></p></div>';
        echo '<div class="notice notice-info"><p>POST data: <pre>' . print_r($_POST, true) . '</pre></p></div>';
    }

    $updated_settings = array(
        'business_name' => si_sanitize_text($_POST['business_name'] ?? ''),
        'business_address' => si_sanitize_textarea($_POST['business_address'] ?? ''),
        'business_email' => si_sanitize_email($_POST['business_email'] ?? ''),
        'business_phone' => si_sanitize_text($_POST['business_phone'] ?? ''),
        'business_logo' => si_sanitize_url($_POST['business_logo'] ?? ''),
        'gstin' => si_sanitize_text($_POST['gstin'] ?? ''),
        'default_due_days' => intval($_POST['default_due_days'] ?? 7),
        'payment_methods' => isset($_POST['payment_methods']) && is_array($_POST['payment_methods'])
            ? array_map('si_sanitize_text', $_POST['payment_methods'])
            : array(),
        'bank_details' => si_sanitize_textarea($_POST['bank_details'] ?? ''),
        'paypal_email' => si_sanitize_email($_POST['paypal_email'] ?? ''),
        'upi_id' => si_sanitize_text($_POST['upi_id'] ?? ''),
        'footer_notes' => si_sanitize_textarea($_POST['footer_notes'] ?? ''),
        'terms_text' => si_sanitize_textarea($_POST['terms_text'] ?? ''),
        'clear_data_on_deactivation' => isset($_POST['clear_data_on_deactivation']) ? 1 : 0
    );

    error_log('Updated settings array: ' . print_r($updated_settings, true));

    // Use the working si_update_settings function
    $result = si_update_settings($updated_settings);
    error_log('si_update_settings result: ' . var_export($result, true));

    if ($result) {
        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'simple-invoice') . '</p></div>';

        // Force refresh the settings variable so form shows updated values
        // Get fresh settings data (bypass any caching)
        $settings = si_get_settings('', true);

        error_log('Settings saved successfully. Refreshed settings: ' . print_r($settings, true));

        // Add debug output to verify the refresh worked
        if (isset($_GET['debug'])) {
            echo '<div class="notice notice-info"><p><strong>DEBUG: Settings after save refresh:</strong><br><pre>' . print_r($settings, true) . '</pre></p></div>';

            // Also check direct database value
            $direct_db_value = get_option('si_settings');
            echo '<div class="notice notice-info"><p><strong>DEBUG: Direct database value:</strong><br><pre>' . print_r($direct_db_value, true) . '</pre></p></div>';
        }
    } else {
        echo '<div class="notice notice-error"><p>' . __('Failed to save settings.', 'simple-invoice') . '</p></div>';
        error_log('Failed to save settings');
    }
} else {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        error_log('POST received but form submission not detected');
        error_log('save_settings isset: ' . (isset($_POST['save_settings']) ? 'yes' : 'no'));
        error_log('nonce verification: ' . (wp_verify_nonce($_POST['_wpnonce'] ?? '', 'si_settings_nonce') ? 'passed' : 'failed'));
    }
}

// Handle debug save
if (isset($_POST['debug_save']) && wp_verify_nonce($_POST['_wpnonce'], 'si_settings_nonce')) {
    echo '<div class="notice notice-info"><p><strong>DEBUG SAVE TRIGGERED</strong></p></div>';
    echo '<div class="notice notice-info"><p>POST data: <pre>' . print_r($_POST, true) . '</pre></p></div>';

    // Test basic WordPress options functionality
    $test_option_name = 'si_debug_test_' . time();
    $test_option_value = array('test' => 'value', 'timestamp' => time());

    echo '<div class="notice notice-info"><p><strong>Testing WordPress options system:</strong></p></div>';

    // Test add_option
    $add_result = add_option($test_option_name, $test_option_value);
    echo '<div class="notice notice-info"><p>add_option result: ' . ($add_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    // Test get_option
    $get_result = get_option($test_option_name);
    echo '<div class="notice notice-info"><p>get_option result: <pre>' . print_r($get_result, true) . '</pre></p></div>';

    // Test update_option
    $test_option_value['updated'] = true;
    $update_result = update_option($test_option_name, $test_option_value);
    echo '<div class="notice notice-info"><p>update_option result: ' . ($update_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    // Verify update
    $verify_result = get_option($test_option_name);
    echo '<div class="notice notice-info"><p>Verified updated option: <pre>' . print_r($verify_result, true) . '</pre></p></div>';

    // Clean up test option
    delete_option($test_option_name);

    echo '<div class="notice notice-info"><p><strong>Testing si_settings specifically:</strong></p></div>';

    // Check if si_settings option exists
    $current_si_settings = get_option('si_settings');
    echo '<div class="notice notice-info"><p>Current si_settings option: <pre>' . print_r($current_si_settings, true) . '</pre></p></div>';

    // Test direct update_option on si_settings
    $test_settings = array(
        'business_name' => 'Debug Test Business',
        'business_email' => '<EMAIL>',
        'debug_timestamp' => time()
    );

    $direct_result = update_option('si_settings', $test_settings);
    echo '<div class="notice notice-info"><p>Direct update_option on si_settings result: ' . ($direct_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    // Verify direct update
    $verify_direct = get_option('si_settings');
    echo '<div class="notice notice-info"><p>Verified si_settings after direct update: <pre>' . print_r($verify_direct, true) . '</pre></p></div>';

    // Now test si_update_settings function with simple data
    echo '<div class="notice notice-info"><p><strong>Testing si_update_settings with simple data:</strong></p></div>';

    $simple_test = array(
        'business_name' => 'Simple Test Name',
        'business_email' => '<EMAIL>'
    );

    $result = si_update_settings($simple_test);
    echo '<div class="notice notice-info"><p>si_update_settings with simple data result: ' . ($result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    $verify = si_get_settings();
    echo '<div class="notice notice-info"><p>Settings after simple test: <pre>' . print_r($verify, true) . '</pre></p></div>';

    // Test with the actual form data
    echo '<div class="notice notice-info"><p><strong>Testing si_update_settings with form data:</strong></p></div>';

    $form_test = array(
        'business_name' => $_POST['business_name'] ?? 'Form Test Business',
        'business_address' => $_POST['business_address'] ?? 'Form Test Address',
        'business_email' => $_POST['business_email'] ?? '<EMAIL>',
        'business_phone' => $_POST['business_phone'] ?? '**********'
    );

    $form_result = si_update_settings($form_test);
    echo '<div class="notice notice-info"><p>si_update_settings with form data result: ' . ($form_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    $final_verify = si_get_settings();
    echo '<div class="notice notice-info"><p>Final settings after form test: <pre>' . print_r($final_verify, true) . '</pre></p></div>';

    // IMPORTANT: Update the $settings variable so the form shows the latest data
    $settings = $final_verify;
    echo '<div class="notice notice-success"><p><strong>Settings variable updated for form display!</strong></p></div>';
}

// Handle clear data submission
if (isset($_POST['clear_data_submit']) && wp_verify_nonce($_POST['_wpnonce'], 'si_clear_data_nonce')) {
    $clear_options = array();

    if (isset($_POST['clear_clients'])) $clear_options[] = 'clients';
    if (isset($_POST['clear_templates'])) $clear_options[] = 'templates';
    if (isset($_POST['clear_invoices'])) $clear_options[] = 'invoices';
    if (isset($_POST['clear_settings'])) $clear_options[] = 'settings';

    if (!empty($clear_options)) {
        $result = si_clear_plugin_data($clear_options);
        if ($result) {
            echo '<div class="notice notice-success"><p>' . __('Selected data cleared successfully.', 'simple-invoice') . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . __('Failed to clear some data. Please try again.', 'simple-invoice') . '</p></div>';
        }
    } else {
        echo '<div class="notice notice-warning"><p>' . __('Please select at least one data type to clear.', 'simple-invoice') . '</p></div>';
    }
}
?>

<div class="wrap si-settings-wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html__('Simple Invoice Settings', 'simple-invoice'); ?></h1>
    <a href="<?php echo esc_url(admin_url('admin.php?page=simple-invoice')); ?>" class="page-title-action">
        <span class="dashicons dashicons-arrow-left-alt"></span>
        <?php echo esc_html__('Back to Dashboard', 'simple-invoice'); ?>
    </a>

    <hr class="wp-header-end">

    <!-- Settings Navigation Tabs -->
    <div class="si-settings-nav">
        <nav class="nav-tab-wrapper">
            <a href="#business-info" class="nav-tab nav-tab-active" data-tab="business-info">
                <span class="dashicons dashicons-building"></span>
                <?php echo esc_html__('Business Info', 'simple-invoice'); ?>
            </a>
            <a href="#invoice-settings" class="nav-tab" data-tab="invoice-settings">
                <span class="dashicons dashicons-media-text"></span>
                <?php echo esc_html__('Invoice Settings', 'simple-invoice'); ?>
            </a>
            <a href="#payment-settings" class="nav-tab" data-tab="payment-settings">
                <span class="dashicons dashicons-money-alt"></span>
                <?php echo esc_html__('Payment Settings', 'simple-invoice'); ?>
            </a>
            <a href="#footer-settings" class="nav-tab" data-tab="footer-settings">
                <span class="dashicons dashicons-editor-alignleft"></span>
                <?php echo esc_html__('Footer & Terms', 'simple-invoice'); ?>
            </a>
            <a href="#clear-data" class="nav-tab" data-tab="clear-data">
                <span class="dashicons dashicons-trash"></span>
                <?php echo esc_html__('Clear Data', 'simple-invoice'); ?>
            </a>
        </nav>
    </div>

    <?php if (isset($_GET['debug'])): ?>
        <div class="notice notice-info">
            <p><strong>DEBUG: Form will be populated with these values:</strong></p>
            <ul>
                <li>Business Name: "<?php echo esc_html($settings['business_name'] ?? 'EMPTY'); ?>"</li>
                <li>Business Address: "<?php echo esc_html($settings['business_address'] ?? 'EMPTY'); ?>"</li>
                <li>Business Email: "<?php echo esc_html($settings['business_email'] ?? 'EMPTY'); ?>"</li>
                <li>Business Phone: "<?php echo esc_html($settings['business_phone'] ?? 'EMPTY'); ?>"</li>
            </ul>
        </div>
    <?php endif; ?>

    <form method="post" action="" class="si-settings-form">
        <?php wp_nonce_field('si_settings_nonce'); ?>
        
        <div class="si-settings-container">

            <!-- Business Information Tab -->
            <div class="si-settings-tab si-tab-content" id="business-info">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-building"></span>
                            <?php echo esc_html__('Business Information', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Configure your business details that will appear on invoices and communications.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="business_name"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="business_name" 
                                   name="business_name" 
                                   value="<?php echo esc_attr($settings['business_name'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Enter your business name', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('This will appear on your invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_address"><?php echo esc_html__('Business Address', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="business_address" 
                                      name="business_address" 
                                      rows="4" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Enter your complete business address', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['business_address'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Your business address for invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_email"><?php echo esc_html__('Business Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email" 
                                   id="business_email" 
                                   name="business_email" 
                                   value="<?php echo esc_attr($settings['business_email'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Contact email for your business.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_phone"><?php echo esc_html__('Business Phone', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="business_phone" 
                                   name="business_phone" 
                                   value="<?php echo esc_attr($settings['business_phone'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('+****************', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Contact phone number for your business.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_logo"><?php echo esc_html__('Business Logo', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="url" 
                                   id="business_logo" 
                                   name="business_logo" 
                                   value="<?php echo esc_attr($settings['business_logo'] ?? ''); ?>" 
                                   class="regular-text si-media-input" 
                                   placeholder="<?php echo esc_attr__('Logo URL', 'simple-invoice'); ?>" />
                            <button type="button" class="button si-media-button" data-target="business_logo">
                                <?php echo esc_html__('Select Logo', 'simple-invoice'); ?>
                            </button>
                            <p class="description"><?php echo esc_html__('Upload or select your business logo. Leave empty to use site logo.', 'simple-invoice'); ?></p>
                            <?php if (!empty($settings['business_logo'])): ?>
                                <div class="si-logo-preview">
                                    <img src="<?php echo esc_url($settings['business_logo']); ?>" alt="<?php echo esc_attr__('Business Logo', 'simple-invoice'); ?>" style="max-width: 200px; height: auto;" />
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="gstin"><?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="gstin" 
                                   name="gstin" 
                                   value="<?php echo esc_attr($settings['gstin'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Enter your GSTIN or Tax ID', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your business tax identification number.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Invoice Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="invoice-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-media-text"></span>
                            <?php echo esc_html__('Invoice Settings', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Configure default settings for your invoices and billing preferences.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_due_days"><?php echo esc_html__('Default Due Days', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="number" 
                                   id="default_due_days" 
                                   name="default_due_days" 
                                   value="<?php echo esc_attr($settings['default_due_days'] ?? 7); ?>" 
                                   class="small-text" 
                                   min="1" 
                                   max="365" />
                            <p class="description"><?php echo esc_html__('Number of days after invoice date when payment is due.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Payment Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="payment-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-money-alt"></span>
                            <?php echo esc_html__('Payment Settings', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Set up your payment methods and banking information for client payments.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php echo esc_html__('Accepted Payment Methods', 'simple-invoice'); ?></th>
                        <td>
                            <fieldset>
                                <legend class="screen-reader-text"><?php echo esc_html__('Payment Methods', 'simple-invoice'); ?></legend>
                                
                                <label>
                                    <input type="checkbox"
                                           id="bank_enabled"
                                           name="payment_methods[]"
                                           value="bank"
                                           <?php checked(in_array('bank', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('Bank Transfer', 'simple-invoice'); ?>
                                </label><br />

                                <label>
                                    <input type="checkbox"
                                           id="paypal_enabled"
                                           name="payment_methods[]"
                                           value="paypal"
                                           <?php checked(in_array('paypal', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('PayPal', 'simple-invoice'); ?>
                                </label><br />

                                <label>
                                    <input type="checkbox"
                                           id="upi_enabled"
                                           name="payment_methods[]"
                                           value="upi"
                                           <?php checked(in_array('upi', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('UPI Payment', 'simple-invoice'); ?>
                                </label>
                            </fieldset>
                            <p class="description"><?php echo esc_html__('Select the payment methods you accept.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr id="bank_details_row" style="display: <?php echo in_array('bank', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="bank_details"><?php echo esc_html__('Bank Details', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="bank_details"
                                      name="bank_details"
                                      rows="4"
                                      class="large-text"
                                      placeholder="<?php echo esc_attr__('Bank Name: XYZ Bank\nAccount Number: **********\nIFSC Code: ABCD0123456\nAccount Holder: Your Business Name', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['bank_details'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Your bank account details for wire transfers.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr id="paypal_email_row" style="display: <?php echo in_array('paypal', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="paypal_email"><?php echo esc_html__('PayPal Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email"
                                   id="paypal_email"
                                   name="paypal_email"
                                   value="<?php echo esc_attr($settings['paypal_email'] ?? ''); ?>"
                                   class="regular-text"
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your PayPal email address for receiving payments.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr id="upi_id_row" style="display: <?php echo in_array('upi', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="upi_id"><?php echo esc_html__('UPI ID', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text"
                                   id="upi_id"
                                   name="upi_id"
                                   value="<?php echo esc_attr($settings['upi_id'] ?? ''); ?>"
                                   class="regular-text"
                                   placeholder="<?php echo esc_attr__('yourname@paytm', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your UPI ID for generating QR codes on invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Footer Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="footer-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-editor-alignleft"></span>
                            <?php echo esc_html__('Footer & Terms', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Customize footer messages and terms & conditions for your invoices.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="footer_notes"><?php echo esc_html__('Footer Notes', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="footer_notes" 
                                      name="footer_notes" 
                                      rows="3" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Thank you for your business!', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['footer_notes'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Thank you message or additional notes to display at the bottom of invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="terms_text"><?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="terms_text" 
                                      name="terms_text" 
                                      rows="4" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Payment is due within the specified due date. Late payments may incur additional charges.', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['terms_text'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Terms and conditions or refund policy text.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Clear Data Tab -->
            <div class="si-settings-tab si-tab-content si-clear-data-tab" id="clear-data">
                <!-- Simple Test Content First -->
                <div style="padding: 20px; background: #f9f9f9; border: 1px solid #ddd; margin: 20px;">
                    <h2 style="color: #000; margin: 0 0 15px 0;">🗑️ Clear Data Tab</h2>
                    <p style="color: #666; margin: 0 0 20px 0;">This tab allows you to manage and clear plugin data.</p>

                    <!-- Data Overview -->
                    <div style="background: #fff; padding: 15px; border: 1px solid #ddd; margin-bottom: 20px;">
                        <h3 style="margin: 0 0 10px 0; color: #000;">Current Data Overview</h3>
                        <?php
                        // Get data counts
                        if (function_exists('si_get_data_counts')) {
                            $data_counts = si_get_data_counts();
                        } else {
                            $data_counts = array('clients' => 0, 'templates' => 0, 'invoices' => 0);
                        }
                        ?>
                        <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                            <div style="background: #e7e7e7; padding: 10px; border-radius: 5px; text-align: center; min-width: 100px;">
                                <div style="font-size: 20px; font-weight: bold; color: #f47a45;"><?php echo esc_html($data_counts['clients']); ?></div>
                                <div style="font-size: 12px; color: #666;">Clients</div>
                            </div>
                            <div style="background: #e7e7e7; padding: 10px; border-radius: 5px; text-align: center; min-width: 100px;">
                                <div style="font-size: 20px; font-weight: bold; color: #f47a45;"><?php echo esc_html($data_counts['templates']); ?></div>
                                <div style="font-size: 12px; color: #666;">Templates</div>
                            </div>
                            <div style="background: #e7e7e7; padding: 10px; border-radius: 5px; text-align: center; min-width: 100px;">
                                <div style="font-size: 20px; font-weight: bold; color: #f47a45;"><?php echo esc_html($data_counts['invoices']); ?></div>
                                <div style="font-size: 12px; color: #666;">Invoices</div>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Options -->
                    <div style="background: #fff; padding: 15px; border: 1px solid #ddd; margin-bottom: 20px;">
                        <h3 style="margin: 0 0 15px 0; color: #000;">Select Data to Clear</h3>
                        <div style="display: flex; flex-direction: column; gap: 10px;">
                            <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <input type="checkbox" name="clear_clients" value="1" style="margin: 0;">
                                <div>
                                    <strong>Clients</strong><br>
                                    <small style="color: #666;">Delete all client records</small>
                                </div>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <input type="checkbox" name="clear_templates" value="1" style="margin: 0;">
                                <div>
                                    <strong>Templates</strong><br>
                                    <small style="color: #666;">Delete all invoice templates</small>
                                </div>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <input type="checkbox" name="clear_invoices" value="1" style="margin: 0;">
                                <div>
                                    <strong>Invoices</strong><br>
                                    <small style="color: #666;">Delete all generated invoices</small>
                                </div>
                            </label>
                            <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                <input type="checkbox" name="clear_settings" value="1" style="margin: 0;">
                                <div>
                                    <strong>Plugin Settings</strong><br>
                                    <small style="color: #666;">Reset all plugin settings</small>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Actions -->
                    <div style="background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;">
                        <div style="color: #856404; margin-bottom: 15px;">
                            <strong>⚠️ Warning:</strong> This action is permanent and cannot be undone.
                        </div>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button type="button" class="button" id="si-select-all-simple">Select All</button>
                            <button type="button" class="button" id="si-deselect-all-simple">Deselect All</button>
                            <button type="button" class="button button-secondary" id="si-clear-data-simple" disabled style="background: #dc3545; color: white; border-color: #dc3545;">
                                Clear Selected Data
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Plugin Settings Section -->
                <div style="padding: 20px; background: #fff; border: 1px solid #ddd; margin: 20px;">
                    <h3 style="margin: 0 0 15px 0; color: #000;">🔧 Plugin Settings</h3>
                    <div style="background: #f9f9f9; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="clear_data_on_deactivation" value="1" <?php checked(1, $settings['clear_data_on_deactivation'] ?? 0); ?>>
                            <div>
                                <strong>Clear all plugin data when deactivating</strong><br>
                                <small style="color: #666;">When enabled, all data will be permanently deleted when you deactivate the plugin.</small>
                            </div>
                        </label>
                        <div style="background: #fff3cd; padding: 10px; margin-top: 10px; border: 1px solid #ffeaa7; border-radius: 3px; color: #856404;">
                            <strong>⚠️ Important:</strong> This action cannot be undone.
                        </div>
                    </div>
                </div>


            </div>

        </div>

        <!-- Separate Clear Data Tab Container (Outside main container) -->
        <div class="si-clear-data-container" id="clear-data-container" style="display: none;">
            <div style="padding: 20px; background: #f9f9f9; border: 1px solid #ddd; margin: 20px 0;">
                <h2 style="color: #000; margin: 0 0 15px 0;">🗑️ Clear Data Management</h2>
                <p style="color: #666; margin: 0 0 20px 0;">This section allows you to manage and clear plugin data.</p>

                <!-- Data Overview -->
                <div style="background: #fff; padding: 15px; border: 1px solid #ddd; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 10px 0; color: #000;">Current Data Overview</h3>
                    <?php
                    // Get data counts
                    if (function_exists('si_get_data_counts')) {
                        $data_counts = si_get_data_counts();
                    } else {
                        $data_counts = array('clients' => 0, 'templates' => 0, 'invoices' => 0);
                    }
                    ?>
                    <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                        <div style="background: #e7e7e7; padding: 10px; border-radius: 5px; text-align: center; min-width: 100px;">
                            <div style="font-size: 20px; font-weight: bold; color: #f47a45;"><?php echo esc_html($data_counts['clients']); ?></div>
                            <div style="font-size: 12px; color: #666;">Clients</div>
                        </div>
                        <div style="background: #e7e7e7; padding: 10px; border-radius: 5px; text-align: center; min-width: 100px;">
                            <div style="font-size: 20px; font-weight: bold; color: #f47a45;"><?php echo esc_html($data_counts['templates']); ?></div>
                            <div style="font-size: 12px; color: #666;">Templates</div>
                        </div>
                        <div style="background: #e7e7e7; padding: 10px; border-radius: 5px; text-align: center; min-width: 100px;">
                            <div style="font-size: 20px; font-weight: bold; color: #f47a45;"><?php echo esc_html($data_counts['invoices']); ?></div>
                            <div style="font-size: 12px; color: #666;">Invoices</div>
                        </div>
                    </div>
                </div>

                <!-- Clear Options -->
                <div style="background: #fff; padding: 15px; border: 1px solid #ddd; margin-bottom: 20px;">
                    <h3 style="margin: 0 0 15px 0; color: #000;">Select Data to Clear</h3>
                    <div style="display: flex; flex-direction: column; gap: 10px;">
                        <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <input type="checkbox" name="clear_clients" value="1" style="margin: 0;">
                            <div>
                                <strong>Clients</strong><br>
                                <small style="color: #666;">Delete all client records</small>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <input type="checkbox" name="clear_templates" value="1" style="margin: 0;">
                            <div>
                                <strong>Templates</strong><br>
                                <small style="color: #666;">Delete all invoice templates</small>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <input type="checkbox" name="clear_invoices" value="1" style="margin: 0;">
                            <div>
                                <strong>Invoices</strong><br>
                                <small style="color: #666;">Delete all generated invoices</small>
                            </div>
                        </label>
                        <label style="display: flex; align-items: center; gap: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            <input type="checkbox" name="clear_settings" value="1" style="margin: 0;">
                            <div>
                                <strong>Plugin Settings</strong><br>
                                <small style="color: #666;">Reset all plugin settings</small>
                            </div>
                        </label>
                    </div>
                </div>

                <!-- Actions -->
                <div style="background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;">
                    <div style="color: #856404; margin-bottom: 15px;">
                        <strong>⚠️ Warning:</strong> This action is permanent and cannot be undone.
                    </div>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        <button type="button" class="button" id="si-select-all-external">Select All</button>
                        <button type="button" class="button" id="si-deselect-all-external">Deselect All</button>
                        <button type="button" class="button button-secondary" id="si-clear-data-external" disabled style="background: #dc3545; color: white; border-color: #dc3545;">
                            Clear Selected Data
                        </button>
                    </div>
                </div>

                <!-- Plugin Settings -->
                <div style="padding: 20px; background: #fff; border: 1px solid #ddd; margin-top: 20px;">
                    <h3 style="margin: 0 0 15px 0; color: #000;">🔧 Plugin Settings</h3>
                    <div style="background: #f9f9f9; padding: 15px; border: 1px solid #ddd; border-radius: 5px;">
                        <label style="display: flex; align-items: center; gap: 10px;">
                            <input type="checkbox" name="clear_data_on_deactivation" value="1" <?php checked(1, $settings['clear_data_on_deactivation'] ?? 0); ?>>
                            <div>
                                <strong>Clear all plugin data when deactivating</strong><br>
                                <small style="color: #666;">When enabled, all data will be permanently deleted when you deactivate the plugin.</small>
                            </div>
                        </label>
                        <div style="background: #fff3cd; padding: 10px; margin-top: 10px; border: 1px solid #ffeaa7; border-radius: 3px; color: #856404;">
                            <strong>⚠️ Important:</strong> This action cannot be undone.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button (Fixed Position) - Only for main settings -->
        <div class="si-settings-save" id="si-main-save-button">
            <input type="submit" name="save_settings" id="si-save-settings" class="button button-primary" value="<?php echo esc_attr__('Save Settings', 'simple-invoice'); ?>" />
            <input type="submit" name="debug_save" class="button button-secondary" value="Debug Save" style="margin-left: 10px;" />
            <span class="si-save-status"></span>
        </div>
    </form>

    <!-- Clear Data Form - Separate form for clear data functionality -->
    <form method="post" action="" class="si-clear-data-form" id="si-clear-data-form" style="display: none;">
        <?php wp_nonce_field('si_clear_data_nonce'); ?>
        <input type="hidden" name="clear_data_submit" value="1" />
        <input type="hidden" name="clear_clients" id="hidden_clear_clients" value="" />
        <input type="hidden" name="clear_templates" id="hidden_clear_templates" value="" />
        <input type="hidden" name="clear_invoices" id="hidden_clear_invoices" value="" />
        <input type="hidden" name="clear_settings" id="hidden_clear_settings" value="" />
    </form>
</div>

<style>
/* Settings Page Styling */
.si-settings-wrap {
    background: #ffffff;
    margin: 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

.wp-heading-inline {
    color: #000000;
    font-size: 28px;
    font-weight: 700;
}

.page-title-action {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff !important;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.page-title-action:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff !important;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Enhanced Tab Styling */
.si-settings-nav {
    margin: 20px 0;
    border-bottom: 2px solid #e7e7e7;
}

.nav-tab-wrapper {
    border-bottom: none;
    margin: 0;
    padding: 0;
}

.nav-tab {
    background: #e7e7e7;
    color: #5f5f5f;
    border: 1px solid #e7e7e7;
    border-bottom: none;
    padding: 12px 20px;
    margin: 0 2px 0 0;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tab:hover {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-2px);
}

.nav-tab-active,
.nav-tab-active:hover {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border-color: #f47a45;
    transform: translateY(0);
}

.nav-tab-active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: #f47a45;
}

.nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Settings Container */
.si-settings-container {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 0 8px 8px 8px;
    padding: 0;
    margin-top: -1px;
}

/* Tab Content Display Fix - Remove !important from hide rule */
.si-settings-container .si-tab-content {
    display: none;
}

.si-settings-container .si-tab-content.si-tab-active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Clear Data Tab Specific Rules */
.si-clear-data-tab {
    display: none;
}

.si-clear-data-tab.si-tab-active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    height: auto !important;
    overflow: visible !important;
    position: relative !important;
}

/* Force Clear Data Tab to always be ready to show */
#clear-data {
    min-height: 100px;
}

/* Hide other tabs when not active */
.si-settings-container .si-tab-content:not(.si-tab-active) {
    display: none !important;
}

.si-settings-section {
    padding: 30px;
}

.si-section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e7e7e7;
}

.si-section-header h2 {
    color: #000000;
    font-size: 22px;
    font-weight: 700;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-section-header .dashicons {
    color: #f47a45;
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.si-section-description {
    color: #5f5f5f;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* Form Table Styling */
.form-table th {
    color: #000000;
    font-weight: 600;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table input[type="text"],
.form-table input[type="email"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table textarea {
    border: 2px solid #e7e7e7;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-table input[type="text"]:focus,
.form-table input[type="email"]:focus,
.form-table input[type="url"]:focus,
.form-table input[type="number"]:focus,
.form-table textarea:focus {
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
    outline: none;
}

.description {
    color: #5f5f5f;
    font-style: italic;
}

/* Clear Data Tab Specific Styling */
.si-data-overview {
    background: #f8f9fa;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 25px;
}

.si-data-overview h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.si-data-stats {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.si-stat-item {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 6px;
    padding: 15px 20px;
    text-align: center;
    min-width: 120px;
}

.si-stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #f47a45;
    margin-bottom: 5px;
}

.si-stat-label {
    display: block;
    font-size: 14px;
    color: #5f5f5f;
    font-weight: 500;
}

.si-clear-options {
    margin-bottom: 25px;
}

.si-clear-options h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 10px 0;
}

.si-clear-checkboxes {
    display: grid;
    gap: 15px;
    margin-top: 15px;
}

.si-clear-option {
    background: #ffffff;
    border: 2px solid #e7e7e7;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.si-clear-option:hover {
    border-color: #f47a45;
    background: #fef9f7;
}

.si-clear-label {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    margin: 0;
}

.si-clear-label input[type="checkbox"] {
    margin: 0;
}

.si-option-content {
    flex: 1;
}

.si-option-content strong {
    display: block;
    color: #000000;
    font-size: 16px;
    margin-bottom: 4px;
}

.si-option-description {
    color: #5f5f5f;
    font-size: 14px;
}

.si-clear-actions {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
}

.si-clear-warning {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #856404;
    margin-bottom: 15px;
}

.si-clear-warning .dashicons {
    color: #f47a45;
}

.si-clear-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.si-clear-buttons button {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
}

#si-clear-data-btn {
    background: #dc3545;
    color: #ffffff;
    border-color: #dc3545;
}

#si-clear-data-btn:hover:not(:disabled) {
    background: #c82333;
    border-color: #bd2130;
}

#si-clear-data-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Save Button */
.si-settings-save {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #ffffff;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border: 1px solid #e7e7e7;
    z-index: 1000;
}

#si-save-settings {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto !important;
    z-index: 1001;
    position: relative;
}

#si-save-settings:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    transform: translateY(-2px);
}

#si-save-settings:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Plugin Settings Section */
.si-plugin-settings-section {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 12px;
    margin-bottom: 20px;
}

.si-section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
}

.si-section-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.si-setting-card {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.si-setting-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.si-setting-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.si-setting-info h3 {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.si-setting-info p {
    color: #5f5f5f;
    font-size: 14px;
    margin: 0;
}

/* Toggle Switch */
.si-toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.si-toggle-switch input[type="checkbox"] {
    display: none;
}

.si-toggle-slider {
    width: 50px;
    height: 26px;
    background: #e7e7e7;
    border-radius: 13px;
    position: relative;
    transition: background 0.3s ease;
}

.si-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.si-toggle-switch input[type="checkbox"]:checked + .si-toggle-slider {
    background: #f47a45;
}

.si-toggle-switch input[type="checkbox"]:checked + .si-toggle-slider::before {
    transform: translateX(24px);
}

.si-toggle-label {
    color: #000000;
    font-weight: 500;
}

/* Warning Box */
.si-setting-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin-top: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.si-warning-icon {
    color: #f47a45;
    flex-shrink: 0;
    margin-top: 2px;
}

.si-warning-content {
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.4;
}

.si-warning-content strong {
    color: #000000;
}

/* Plugin Settings Save Button */
.si-plugin-settings-save {
    padding: 20px 30px;
    border-top: 1px solid #e7e7e7;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
}

.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    transform: translateY(-2px);
}

/* Data Overview */
.si-data-overview {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.si-data-overview h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.si-data-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.si-data-stats .si-stat-item {
    background: #e7e7e7;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.si-data-stats .si-stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #f47a45;
    display: block;
    margin-bottom: 5px;
}

.si-data-stats .si-stat-label {
    font-size: 12px;
    color: #5f5f5f;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Clear Data Options */
.si-clear-options {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.si-clear-options h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.si-clear-checkboxes {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
}

.si-clear-option {
    background: #ffffff;
    border: 2px solid #e7e7e7;
    border-radius: 8px;
    padding: 15px;
    transition: all 0.3s ease;
}

.si-clear-option:hover {
    border-color: #f47a45;
    box-shadow: 0 2px 8px rgba(244, 122, 69, 0.1);
}

.si-clear-label {
    display: flex;
    align-items: center;
    gap: 15px;
    cursor: pointer;
    margin: 0;
}

.si-clear-label input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #f47a45;
}

.si-option-content strong {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    display: block;
    margin-bottom: 5px;
}

.si-option-description {
    color: #5f5f5f;
    font-size: 14px;
}

.si-clear-actions {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.si-clear-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #856404;
}

.si-clear-warning .dashicons {
    color: #f39c12;
    font-size: 20px;
}

.si-clear-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

#si-clear-data-btn {
    background: #dc3545;
    color: #ffffff;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

#si-clear-data-btn:hover:not(:disabled) {
    background: #c82333;
    transform: translateY(-1px);
}

#si-clear-data-btn:disabled {
    background: #e7e7e7;
    color: #5f5f5f;
    cursor: not-allowed;
    transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-settings-save {
        position: static;
        margin-top: 20px;
        box-shadow: none;
        border: 1px solid #e7e7e7;
    }

    .nav-tab {
        padding: 10px 15px;
        font-size: 13px;
    }

    .si-section-header h2 {
        font-size: 20px;
    }

    .si-setting-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    console.log('Simple Invoice Settings Page JavaScript loaded');
    console.log('Clear Data tab element exists:', $('#clear-data').length > 0);
    console.log('Clear Data tab content:', $('#clear-data').html() ? 'Has content' : 'Empty');

    // Tab functionality
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();

        var targetTab = $(this).data('tab');
        console.log('Tab clicked:', targetTab);

        // Remove active class from all tabs
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        // Hide all tab content properly
        $('.si-tab-content').removeClass('si-tab-active').hide().css('display', 'none');

        // Show target tab content with proper isolation
        if (targetTab === 'clear-data') {
            // Hide main settings container and show external clear data container
            $('.si-settings-container').hide();
            $('#clear-data-container').show();
        } else {
            // Show main settings container and hide external clear data container
            $('.si-settings-container').show();
            $('#clear-data-container').hide();

            var targetElement = $('#' + targetTab);
            targetElement.attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important;');
            targetElement.addClass('si-tab-active').show();
        }

        console.log('Showing tab content for:', targetTab);
        console.log('Tab content element found:', targetElement.length);
        console.log('Tab content is visible:', targetElement.is(':visible'));
        console.log('Tab content CSS display:', targetElement.css('display'));
        console.log('Tab content has active class:', targetElement.hasClass('si-tab-active'));
        console.log('Tab content inline style:', targetElement.attr('style'));

        // Show/hide appropriate save buttons
        if (targetTab === 'clear-data') {
            $('#si-main-save-button').hide();
            console.log('Save button hidden for Clear Data tab');
        } else {
            $('#si-main-save-button').show();
            console.log('Save button shown for tab:', targetTab);
        }

        // Update URL hash
        window.location.hash = targetTab;
    });

    // Check for hash on page load and activate appropriate tab
    if (window.location.hash) {
        var hash = window.location.hash.substring(1);
        console.log('Hash found on page load:', hash);
        var targetTab = $('.nav-tab[data-tab="' + hash + '"]');
        console.log('Target tab found:', targetTab.length);
        if (targetTab.length) {
            targetTab.trigger('click');

            // Special handling for Clear Data tab
            if (hash === 'clear-data') {
                setTimeout(function() {
                    console.log('Fallback: Force showing Clear Data external container');
                    $('.si-settings-container').hide();
                    $('#clear-data-container').show();
                    $('.nav-tab').removeClass('nav-tab-active');
                    $('.nav-tab[data-tab="clear-data"]').addClass('nav-tab-active');
                    $('#si-main-save-button').hide();
                    console.log('Fallback complete. Clear Data container visible:', $('#clear-data-container').is(':visible'));
                }, 500);
            }
        }
    } else {
        // Default to business-info tab if no hash
        $('.nav-tab[data-tab="business-info"]').trigger('click');
    }

    // Debug: Check if Clear Data tab exists
    console.log('Clear Data tab content exists:', $('#clear-data').length);
    console.log('Clear Data tab content visible:', $('#clear-data').is(':visible'));

    // Ensure Clear Data tab shows properly when clicked
    $('.nav-tab[data-tab="clear-data"]').on('click', function(e) {
        e.preventDefault();
        console.log('Clear Data tab clicked directly');

        // Remove active class from all tabs
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        // Hide main settings container and show external clear data container
        $('.si-settings-container').hide();
        $('#clear-data-container').show();

        // Hide save button for Clear Data tab
        $('#si-main-save-button').hide();

        // Update URL hash
        window.location.hash = 'clear-data';

        console.log('Clear Data external container should now be visible:', $('#clear-data-container').is(':visible'));
    });

    // Also add a manual trigger for testing
    window.showClearDataTab = function() {
        $('.si-tab-content').hide();
        $('#clear-data').show();
        $('.nav-tab').removeClass('nav-tab-active');
        $('.nav-tab[data-tab="clear-data"]').addClass('nav-tab-active');
        console.log('Manual Clear Data tab show triggered');
    };

    // Clear Data Tab Functionality
    $('#si-select-all-data').on('click', function() {
        $('#clear-data input[type="checkbox"]').prop('checked', true);
        updateClearDataButton();
    });

    $('#si-deselect-all-data').on('click', function() {
        $('#clear-data input[type="checkbox"]').prop('checked', false);
        updateClearDataButton();
    });

    $('#clear-data input[type="checkbox"]').on('change', function() {
        updateClearDataButton();
    });

    function updateClearDataButton() {
        var hasChecked = $('#clear-data input[type="checkbox"]:checked').length > 0;
        $('#si-clear-data-btn').prop('disabled', !hasChecked);
    }

    $('#si-clear-data-btn').on('click', function(e) {
        e.preventDefault();

        var checkedItems = [];
        $('#clear-data input[type="checkbox"]:checked').each(function() {
            var name = $(this).attr('name');
            var label = $(this).closest('.si-clear-option').find('strong').text();
            checkedItems.push(label);
        });

        if (checkedItems.length === 0) {
            alert('Please select at least one item to clear.');
            return;
        }

        var confirmMessage = 'Are you sure you want to permanently delete the following data?\n\n';
        confirmMessage += '• ' + checkedItems.join('\n• ');
        confirmMessage += '\n\nThis action cannot be undone!';

        if (confirm(confirmMessage)) {
            // Copy checkbox values to hidden form
            $('#clear-data input[type="checkbox"]').each(function() {
                var name = $(this).attr('name');
                var value = $(this).is(':checked') ? '1' : '';
                $('#hidden_' + name).val(value);
            });

            // Show loading state
            $(this).prop('disabled', true).text('Clearing Data...');

            // Submit the clear data form
            $('#si-clear-data-form').submit();
        }
    });

    // Initialize save button visibility and clear data functionality
    var currentTab = $('.nav-tab-active').data('tab');
    if (currentTab === 'clear-data') {
        $('#si-main-save-button').hide();
    } else {
        $('#si-main-save-button').show();
    }

    // Initialize clear data button state
    function updateClearDataButton() {
        var hasChecked = $('#clear-data input[type="checkbox"]:checked').length > 0;
        $('#si-clear-data-btn').prop('disabled', !hasChecked);
        console.log('Clear data button state updated. Checked items:', hasChecked);
    }

    // Monitor checkbox changes in clear data tab
    $('#clear-data input[type="checkbox"]').on('change', function() {
        updateClearDataButton();
    });

    // Initialize button state
    updateClearDataButton();

    // External Clear Data Container Functionality
    $('#si-select-all-external').on('click', function() {
        $('#clear-data-container input[type="checkbox"]').prop('checked', true);
        updateExternalClearButton();
    });

    $('#si-deselect-all-external').on('click', function() {
        $('#clear-data-container input[type="checkbox"]').prop('checked', false);
        updateExternalClearButton();
    });

    $('#clear-data-container input[type="checkbox"]').on('change', function() {
        updateExternalClearButton();
    });

    function updateExternalClearButton() {
        var hasChecked = $('#clear-data-container input[type="checkbox"]:checked').length > 0;
        $('#si-clear-data-external').prop('disabled', !hasChecked);
    }

    $('#si-clear-data-external').on('click', function() {
        var checkedItems = [];
        $('#clear-data-container input[type="checkbox"]:checked').each(function() {
            var name = $(this).attr('name');
            var label = $(this).closest('label').find('strong').text();
            checkedItems.push(label);
        });

        if (checkedItems.length === 0) {
            alert('Please select at least one item to clear.');
            return;
        }

        var confirmMessage = 'Are you sure you want to permanently delete the following data?\n\n';
        confirmMessage += '• ' + checkedItems.join('\n• ');
        confirmMessage += '\n\nThis action cannot be undone!';

        if (confirm(confirmMessage)) {
            // Copy checkbox values to hidden form
            $('#clear-data-container input[type="checkbox"]').each(function() {
                var name = $(this).attr('name');
                var value = $(this).is(':checked') ? '1' : '';
                $('#hidden_' + name).val(value);
            });

            // Show loading state
            $(this).prop('disabled', true).text('Clearing Data...');

            // Submit the clear data form
            $('#si-clear-data-form').submit();
        }
    });

    // Initialize external clear button state
    updateExternalClearButton();

    // Add a test button to manually show Clear Data tab (for debugging)
    if (window.location.search.includes('debug=1')) {
        $('body').append('<button id="test-clear-data" style="position:fixed;top:50px;right:20px;z-index:9999;background:red;color:white;padding:10px;">Test Clear Data Tab</button>');
        $('body').append('<button id="test-inject-content" style="position:fixed;top:100px;right:20px;z-index:9999;background:green;color:white;padding:10px;">Inject Test Content</button>');

        $('#test-clear-data').on('click', function() {
            console.log('Manual test: Showing Clear Data tab');
            $('.si-tab-content').hide().css('display', 'none');
            var clearDataTab = $('#clear-data');
            clearDataTab.addClass('si-tab-active');
            clearDataTab.attr('style', 'display: block !important; visibility: visible !important; opacity: 1 !important; min-height: 500px;');
            clearDataTab.show();
            $('.nav-tab').removeClass('nav-tab-active');
            $('.nav-tab[data-tab="clear-data"]').addClass('nav-tab-active');
            $('#si-main-save-button').hide();
            console.log('Clear Data tab manually shown. Visible:', clearDataTab.is(':visible'));
            console.log('Clear Data tab CSS display:', clearDataTab.css('display'));
            console.log('Clear Data tab style attr:', clearDataTab.attr('style'));
        });

        $('#test-inject-content').on('click', function() {
            console.log('Injecting test content directly');
            $('.si-settings-container').append('<div id="test-content" style="display: block !important; background: yellow; padding: 20px; border: 3px solid green; margin: 20px;"><h2>TEST CONTENT - Clear Data Tab</h2><p>This is a test to see if content can be displayed in this area.</p></div>');
            $('.nav-tab').removeClass('nav-tab-active');
            $('.nav-tab[data-tab="clear-data"]').addClass('nav-tab-active');
            $('#si-main-save-button').hide();
        });
    }

    // Media uploader for logo
    $('.si-media-button').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var targetInput = $('#' + button.data('target'));

        var mediaUploader = wp.media({
            title: '<?php echo esc_js(__('Select Business Logo', 'simple-invoice')); ?>',
            button: {
                text: '<?php echo esc_js(__('Use this image', 'simple-invoice')); ?>'
            },
            multiple: false
        });

        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            targetInput.val(attachment.url);

            // Update preview if exists
            var preview = targetInput.closest('td').find('.si-logo-preview');
            if (preview.length) {
                preview.find('img').attr('src', attachment.url);
            } else {
                targetInput.after('<div class="si-logo-preview"><img src="' + attachment.url + '" alt="<?php echo esc_attr__('Business Logo', 'simple-invoice'); ?>" style="max-width: 200px; height: auto; margin-top: 10px;" /></div>');
            }
        });

        mediaUploader.open();
    });

    // Payment method field toggles
    function initPaymentToggles() {
        console.log('Initializing payment toggles...');

        // Bank Transfer toggle
        $('#bank_enabled').on('change', function() {
            console.log('Bank checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#bank_details_row').show().fadeIn(300);
            } else {
                $('#bank_details_row').fadeOut(300);
            }
        });

        // PayPal toggle
        $('#paypal_enabled').on('change', function() {
            console.log('PayPal checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#paypal_email_row').show().fadeIn(300);
            } else {
                $('#paypal_email_row').fadeOut(300);
            }
        });

        // UPI toggle
        $('#upi_enabled').on('change', function() {
            console.log('UPI checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#upi_id_row').show().fadeIn(300);
            } else {
                $('#upi_id_row').fadeOut(300);
            }
        });

        console.log('Payment toggles initialized');
    }

    // Initialize payment toggles
    initPaymentToggles();

    // Let the save button submit naturally - no JavaScript interference needed
    $('#si-save-settings').on('click', function(e) {
        console.log('Save button clicked - allowing natural form submission');
        // Don't prevent default - let the form submit naturally
        // The form will submit to the same page and PHP will handle it
    });

    // Auto-save indication
    $('.si-settings-form input, .si-settings-form textarea, .si-settings-form select').on('change', function() {
        $('.si-save-status').text('<?php echo esc_js(__('Unsaved changes', 'simple-invoice')); ?>').removeClass('success').addClass('warning');
    });

    // Clear Data Tab Functionality
    function updateClearButton() {
        var anyChecked = $('.si-clear-checkboxes input[type="checkbox"]:checked').length > 0;
        $('#si-clear-data-btn').prop('disabled', !anyChecked);
    }

    // Monitor checkbox changes
    $('.si-clear-checkboxes input[type="checkbox"]').on('change', function() {
        updateClearButton();
    });

    // Select all data
    $('#si-select-all-data').on('click', function() {
        $('.si-clear-checkboxes input[type="checkbox"]').prop('checked', true);
        updateClearButton();
    });

    // Deselect all data
    $('#si-deselect-all-data').on('click', function() {
        $('.si-clear-checkboxes input[type="checkbox"]').prop('checked', false);
        updateClearButton();
    });

    // Clear data button click handler
    $('#si-clear-data-btn').on('click', function(e) {
        e.preventDefault();

        var checkedItems = $('.si-clear-checkboxes input[type="checkbox"]:checked');

        if (checkedItems.length === 0) {
            alert('<?php echo esc_js(__('Please select at least one data type to clear.', 'simple-invoice')); ?>');
            return false;
        }

        var itemNames = [];
        checkedItems.each(function() {
            var label = $(this).closest('.si-clear-option').find('strong').text();
            itemNames.push(label);
        });

        var confirmMessage = '<?php echo esc_js(__('Are you sure you want to permanently delete the following data?', 'simple-invoice')); ?>\n\n';
        confirmMessage += '• ' + itemNames.join('\n• ') + '\n\n';
        confirmMessage += '<?php echo esc_js(__('This action cannot be undone. Do you want to continue?', 'simple-invoice')); ?>';

        if (!confirm(confirmMessage)) {
            return false;
        }

        // Copy checked values to hidden form and submit
        checkedItems.each(function() {
            var name = $(this).attr('name');
            var hiddenInput = $('#hidden_' + name);
            if (hiddenInput.length) {
                hiddenInput.val($(this).val());
            }
        });

        // Submit the hidden form
        $('#si-clear-data-form').submit();
    });

    // Show notification for clear data operations
    function showClearDataNotification(message, type) {
        // Remove existing notifications
        $('.si-clear-notification').remove();

        var notificationClass = type === 'success' ? 'notice-success' : 'notice-error';
        var notification = $('<div class="notice ' + notificationClass + ' si-clear-notification is-dismissible"><p>' + message + '</p></div>');

        // Insert after the clear data form
        $('.si-clear-data-form').before(notification);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);

        // Scroll to notification
        $('html, body').animate({
            scrollTop: notification.offset().top - 100
        }, 500);
    }

    // Initialize clear button state
    updateClearButton();
});
</script>
